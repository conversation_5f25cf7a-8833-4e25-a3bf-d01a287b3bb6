import { Link } from '@remix-run/react';

interface AuthMessageProps {
  title?: string;
  message?: string;
  redirectPath?: string;
}

/**
 * AuthMessage - A component to show a friendly message when users need to authenticate
 */
export function AuthMessage({
  title = 'Authentication Required',
  message = 'You need to be logged in to use this feature',
  redirectPath = '/signin',
}: AuthMessageProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-6 text-center">
      <div className="mb-6 w-16 h-16 flex items-center justify-center rounded-full bg-blue-50 border border-blue-100">
        <div className="i-ph:lock-key text-3xl text-blue-500"></div>
      </div>

      <h2 className="text-2xl font-bold text-bolt-elements-textPrimary mb-2">{title}</h2>
      <p className="text-bolt-elements-textSecondary max-w-md mb-6">{message}</p>

      <Link
        to={redirectPath}
        className="px-5 py-3 rounded-lg bg-blue-500 hover:bg-blue-600 text-white font-medium transition-colors"
      >
        Sign In
      </Link>
    </div>
  );
}
