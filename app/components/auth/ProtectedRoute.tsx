import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { useAuth } from '~/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({ children, redirectTo = '/signin' }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Only redirect after auth is loaded and if there's no user
    if (!loading && !user) {
      // Add the current path as a redirect parameter
      const currentPath = window.location.pathname;
      const redirectUrl = `${redirectTo}?redirectTo=${encodeURIComponent(currentPath)}`;
      navigate(redirectUrl);
    }
  }, [user, loading, navigate, redirectTo]);

  // If still loading, show a simple loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent align-[-0.125em]"></div>
          <p className="mt-2 text-bolt-elements-textSecondary">Loading...</p>
        </div>
      </div>
    );
  }

  // If not loading and we have a user, render the children
  if (!loading && user) {
    return <>{children}</>;
  }

  // Return null for any other case, the redirect will happen via the useEffect
  return null;
}
