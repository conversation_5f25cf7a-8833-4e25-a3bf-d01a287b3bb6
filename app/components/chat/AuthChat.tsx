import { ClientOnly } from 'remix-utils/client-only';
import { useAuth } from '~/contexts/AuthContext';
import { BaseChat } from './BaseChat';
import { Chat } from './Chat.client';

/**
 * AuthChat - A wrapper component that ensures only authenticated users can access the chat functionality
 * For unauthenticated users, it shows a friendly message with a sign-in button
 */
export function AuthChat() {
  const { loading } = useAuth();

  // Show a loading state while authentication state is being determined
  if (loading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent align-[-0.125em]"></div>
          <p className="mt-2 text-bolt-elements-textSecondary">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <ClientOnly fallback={<BaseChat />}>
      {() => {
        const { user } = useAuth();

        if (!user) {
          return (
            <div className="flex flex-col items-center justify-center h-[70vh] p-6">
              <div className="text-center max-w-md">
                <h2 className="text-2xl font-bold text-bolt-elements-textPrimary mb-4">Welcome to Bolt</h2>
                <p className="mb-6 text-bolt-elements-textSecondary">
                  Please sign in to start a conversation with our AI assistant.
                </p>
                <a
                  href="/signin"
                  className="inline-flex items-center gap-2 px-6 py-3 text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg font-medium shadow-lg hover:shadow-xl hover:opacity-90 transition-all duration-300"
                >
                  Sign In to Continue
                </a>
              </div>
            </div>
          );
        }

        return <Chat />;
      }}
    </ClientOnly>
  );
}
