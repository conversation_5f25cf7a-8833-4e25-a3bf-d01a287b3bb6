.BaseChat {
  &[data-chat-visible='false'] {
    --workbench-inner-width: 100%;
    --workbench-left: 0;

    .Chat {
      --at-apply: bolt-ease-cubic-bezier;
      transition-property: transform, opacity;
      transition-duration: 0.3s;
      will-change: transform, opacity;
      transform: translateX(-50%);
      opacity: 0;
    }
  }
}

.Chat {
  opacity: 1;
}

.PromptEffectContainer {
  --prompt-container-offset: 50px;
  --prompt-line-stroke-width: 1px;
  position: absolute;
  pointer-events: none;
  inset: calc(var(--prompt-container-offset) / -2);
  width: calc(100% + var(--prompt-container-offset));
  height: calc(100% + var(--prompt-container-offset));
}

.PromptEffectLine {
  width: calc(100% - var(--prompt-container-offset) + var(--prompt-line-stroke-width));
  height: calc(100% - var(--prompt-container-offset) + var(--prompt-line-stroke-width));
  x: calc(var(--prompt-container-offset) / 2 - var(--prompt-line-stroke-width) / 2);
  y: calc(var(--prompt-container-offset) / 2 - var(--prompt-line-stroke-width) / 2);
  rx: calc(8px - var(--prompt-line-stroke-width));
  fill: transparent;
  stroke-width: var(--prompt-line-stroke-width);
  stroke: url(#line-gradient);
  stroke-dasharray: 35px 65px;
  stroke-dashoffset: 10;
}

.PromptShine {
  /* Removing shine effect */
  fill: transparent;
  mix-blend-mode: normal;
}
