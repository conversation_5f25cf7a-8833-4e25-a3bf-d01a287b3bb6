import React from 'react';

const EXAMPLE_PROMPTS = [
  // Mobile Apps
  { text: 'Create a fitness tracking app' },
  { text: 'Build a recipe finder app' },

  // Web Apps
  { text: 'Design a portfolio with glassmorphism' },
  { text: 'Create a collaborative whiteboard' },
  { text: 'Build a data visualization dashboard' },
];

export function ExamplePrompts(sendMessage?: { (event: React.UIEvent, messageInput?: string): void | undefined }) {
  return (
    <div id="examples" className="relative flex flex-col gap-9 w-full max-w-3xl mx-auto flex justify-center mt-6">
      <div className="flex flex-wrap justify-center gap-2">
        {EXAMPLE_PROMPTS.map((examplePrompt, index: number) => {
          return (
            <button
              key={index}
              onClick={(event) => {
                sendMessage?.(event, examplePrompt.text);
              }}
              className="border border-bolt-elements-borderColor rounded-full bg-gray-50 hover:bg-gray-100 dark:bg-gray-950 dark:hover:bg-gray-900 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary px-3 py-1 text-xs transition-all transform hover:scale-105 animate-fade-in"
              style={{ animationDelay: `${400 + index * 100}ms` }}
            >
              {examplePrompt.text}
            </button>
          );
        })}
      </div>
    </div>
  );
}
