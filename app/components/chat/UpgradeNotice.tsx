import React from 'react';
import { messageLimitStore, MESSAGE_LIMIT_FREE_TIER } from '~/lib/stores/messageLimit';

interface UpgradeNoticeProps {
  onUpgradeClick: () => void;
}

export const UpgradeNotice: React.FC<UpgradeNoticeProps> = ({ onUpgradeClick }) => {
  const messageLimit = messageLimitStore.get();
  const remainingMessages = messageLimit?.remainingMessages || 0;
  const usedMessages = MESSAGE_LIMIT_FREE_TIER - remainingMessages;
  const progressPercentage = (usedMessages / MESSAGE_LIMIT_FREE_TIER) * 100;

  if (remainingMessages >= MESSAGE_LIMIT_FREE_TIER || remainingMessages <= 0) {
    return null;
  }

  return (
    <div className="mx-auto w-full mb-2 bg-gray-900 rounded-lg border border-gray-800 shadow-lg overflow-hidden">
      <div className="flex items-center justify-between px-3 py-2">
        {/* Left side with usage info */}
        <div className="flex items-center gap-2">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 mr-1 flex-shrink-0">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M20 7L12 3L4 7M20 7L12 11M20 7V17L12 21M12 11L4 7M12 11V21M4 7V17L12 21"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div>
            <div className="flex items-center">
              <span className="text-xs text-gray-300 mr-1">
                Usage: {usedMessages}/{MESSAGE_LIMIT_FREE_TIER}
              </span>
              <span className="text-xs font-medium text-blue-400">{remainingMessages} left</span>
            </div>
            <div className="w-28 bg-gray-800 rounded-full h-1.5 mt-1">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-1.5 rounded-full"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
        {/* Right side with upgrade button */}
        <button
          onClick={onUpgradeClick}
          className="px-3 py-1 bg-gradient-to-r from-blue-600 to-purple-700 text-white text-xs font-medium rounded transition-all hover:opacity-90 flex items-center"
        >
          <span>Upgrade Now</span>
        </button>
      </div>
    </div>
  );
};
