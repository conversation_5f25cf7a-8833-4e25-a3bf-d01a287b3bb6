import { memo } from 'react';

interface BoltLoaderProps {
  className?: string;
}

export const BoltLoader = memo(({ className = '' }: BoltLoaderProps) => {
  return (
    <div className={`flex justify-center items-center ${className}`}>
      <div className="flex space-x-3">
        <div className="w-2.5 h-2.5 rounded-full bg-blue-500 animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2.5 h-2.5 rounded-full bg-indigo-500 animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2.5 h-2.5 rounded-full bg-blue-500 animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
    </div>
  );
});
