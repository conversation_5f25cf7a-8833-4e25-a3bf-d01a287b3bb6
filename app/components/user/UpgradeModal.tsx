import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const UpgradeModal: React.FC<UpgradeModalProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] rounded-lg bg-zinc-900 p-6 shadow-xl">
          <Dialog.Title className="text-xl font-bold text-white mb-2">Upgrade to Premium</Dialog.Title>
          <Dialog.Description className="text-gray-300 mb-6">
            Unlock unlimited access to Bo<PERSON>'s advanced features.
          </Dialog.Description>

          <div className="flex flex-col gap-6">
            <div className="bg-zinc-800 p-4 rounded-lg">
              <h3 className="font-bold text-white mb-2">Free Plan</h3>
              <ul className="text-gray-300 text-sm space-y-2">
                <li>• 10 messages per day</li>
                <li>• Basic AI responses</li>
                <li>• Standard response time</li>
              </ul>
              <p className="text-gray-400 text-xs mt-4">Your current plan</p>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg">
              <h3 className="font-bold text-white mb-2">Premium Plan</h3>
              <p className="text-xl font-bold text-white mb-4">
                $9.99<span className="text-sm font-normal">/month</span>
              </p>
              <ul className="text-white text-sm space-y-2">
                <li>• Unlimited messages</li>
                <li>• Advanced AI responses</li>
                <li>• Priority response time</li>
                <li>• Custom chat templates</li>
                <li>• Premium support</li>
              </ul>
              <button
                className="mt-6 w-full bg-white text-purple-700 font-bold py-2 rounded-md hover:bg-gray-100 transition-colors"
                onClick={() => {
                  // Implement actual upgrade flow here
                  alert('Upgrade functionality coming soon!');
                  onClose();
                }}
              >
                Upgrade Now
              </button>
            </div>
          </div>

          <Dialog.Close asChild>
            <button
              className="absolute right-4 top-4 rounded-full p-1 text-gray-400 hover:bg-gray-800 hover:text-white font-bold"
              aria-label="Close"
            >
              X
            </button>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
