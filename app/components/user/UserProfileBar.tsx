import { useAuth } from '~/contexts/AuthContext';
import { useState } from 'react';

interface UserProfileBarProps {
  className?: string;
}

export function UserProfileBar({ className = '' }: UserProfileBarProps) {
  const { user, signOut } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Don't render anything if user is not authenticated
  if (!user) {
    return null;
  }

  // <PERSON><PERSON> sign out
  const handleSignOut = async () => {
    setIsLoggingOut(true);

    try {
      await signOut();

      // Redirect happens automatically through auth state change
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Get user's display name or email
  const displayName =
    user?.user_metadata?.full_name || user?.user_metadata?.name || user?.email?.split('@')[0] || 'User';

  // Get user's avatar URL or use default
  const avatarUrl =
    user?.user_metadata?.avatar_url ||
    user?.identities?.[0]?.identity_data?.avatar_url || // Try to get avatar from identity data (OAuth providers)
    `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=random&size=200&bold=true`; // Improved default avatar

  return (
    <div className={`flex items-center justify-between p-3 border-t border-bolt-elements-borderColor ${className}`}>
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 rounded-full overflow-hidden flex items-center justify-center text-white font-medium">
          {avatarUrl ? (
            <img
              src={avatarUrl}
              alt={user?.email || 'User'}
              className="w-full h-full object-cover"
              loading="eager"
              decoding="sync"
              onError={(e) => {
                // If image fails to load, replace with letter-based avatar
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                e.currentTarget.parentElement!.classList.add('bg-blue-500');
                e.currentTarget.parentElement!.textContent = (user?.email?.charAt(0) || 'U').toUpperCase();
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-blue-500" title="Letter-based avatar">
              {(user?.email?.charAt(0) || 'U').toUpperCase()}
            </div>
          )}
        </div>
        <div>
          <div className="text-sm font-medium text-bolt-elements-textPrimary">{displayName}</div>
          <div className="text-xs text-bolt-elements-textSecondary truncate max-w-[120px]">{user?.email}</div>
        </div>
      </div>

      <button
        onClick={handleSignOut}
        disabled={isLoggingOut}
        className="text-sm py-1.5 px-3 rounded-md bg-slate-200 hover:bg-red-500 text-slate-700 hover:text-white transition-colors duration-200 flex items-center gap-1.5"
      >
        <span className="i-ph-sign-out text-base"></span>
        {isLoggingOut ? 'Signing out...' : 'Sign out'}
      </button>
    </div>
  );
}
