import React, { createContext, useContext, useState, useEffect } from 'react';
import { getSupabaseClient } from '~/lib/supabase/client';
import type { Session, User, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '~/lib/supabase/database.types';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  signIn: {
    email: (
      email: string,
      password: string,
    ) => Promise<{
      error: Error | null;
      data: { user: User | null; session: Session | null } | null;
    }>;
    withGoogle: () => Promise<{ error: Error | null }>;
  };
  signUp: (
    email: string,
    password: string,
  ) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signOut: () => Promise<{ error: Error | null }>;
  loading: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [client, setClient] = useState<SupabaseClient<Database> | null>(null);

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    // Get the Supabase client
    const supabase = getSupabaseClient();
    setClient(supabase);

    // Get initial session
    const initializeAuth = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        const currentSession = data.session;
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, newSession) => {
      setSession(newSession);
      setUser(newSession?.user ?? null);
      setLoading(false);
    });

    // Return cleanup function
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = {
    email: (email: string, password: string) => {
      if (!client) {
        return Promise.resolve({ error: new Error('Client not initialized'), data: null });
      }

      return client.auth.signInWithPassword({ email, password });
    },
    withGoogle: async () => {
      if (!client) {
        return { error: new Error('Client not initialized') };
      }

      /*
       * Support multiple potential redirect URLs by letting Supabase use the current origin.
       * This works as long as you've added all potential redirect URLs in the Supabase dashboard
       * including http://localhost:5173/auth/callback, http://localhost:5174/auth/callback, etc.
       */
      const redirectTo = `${window.location.origin}/auth/callback`;
      console.log('Using redirect URL:', redirectTo);

      return client.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo,
        },
      });
    },
  };

  const signUp = (email: string, password: string) => {
    if (!client) {
      return Promise.resolve({ error: new Error('Client not initialized'), data: null });
    }

    const redirectUrl = `${window.location.origin}/auth/callback`;
    console.log('Using email redirect URL:', redirectUrl);

    return client.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
      },
    });
  };

  const signOut = () => {
    if (!client) {
      return Promise.resolve({ error: new Error('Client not initialized') });
    }

    return client.auth.signOut();
  };

  const value = {
    session,
    user,
    signIn,
    signUp,
    signOut,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
}
