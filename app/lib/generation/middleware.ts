import type { ActionFunction } from '@remix-run/node';
import { GenerationSessionStore } from './sessionStore';
import type { GenerationSession } from '~/types/generation';

export async function withSession(
  request: Request,
  handler: (session: GenerationSession) => Promise<Response>,
): Promise<Response> {
  const url = new URL(request.url);
  const sessionId = url.searchParams.get('sessionId');

  if (!sessionId) {
    return new Response('Session ID is required', { status: 400 });
  }

  const sessionStore = GenerationSessionStore.getInstance();
  const session = sessionStore.getSession(sessionId);

  if (!session) {
    return new Response('Session not found', { status: 404 });
  }

  return handler(session);
}

export async function withNewSession(
  request: Request,
  handler: (session: GenerationSession) => Promise<Response>,
): Promise<Response> {
  const sessionStore = GenerationSessionStore.getInstance();
  const session = sessionStore.createSession();

  return handler(session);
} 