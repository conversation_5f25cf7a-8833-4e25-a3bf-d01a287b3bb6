import { generateId } from '~/utils/fileUtils';
import type { GenerationSession } from '~/types/generation';

export class GenerationSessionStore {
  private static _instance: GenerationSessionStore;
  private _sessions: Map<string, GenerationSession> = new Map();

  private constructor() {}

  static getInstance(): GenerationSessionStore {
    if (!this._instance) {
      this._instance = new GenerationSessionStore();
    }

    return this._instance;
  }

  createSession(): GenerationSession {
    const session: GenerationSession = {
      id: generateId(),
      stage: 'planning',
      status: 'pending',
      data: {},
    };

    this._sessions.set(session.id, session);

    return session;
  }

  getSession(sessionId: string): GenerationSession | undefined {
    return this._sessions.get(sessionId);
  }

  updateSession(sessionId: string, updates: Partial<GenerationSession>): void {
    const session = this._sessions.get(sessionId);

    if (session) {
      this._sessions.set(sessionId, { ...session, ...updates });
    }
  }

  deleteSession(sessionId: string): void {
    this._sessions.delete(sessionId);
  }

  cleanup(): void {
    // Remove completed or errored sessions
    for (const [id, session] of this._sessions.entries()) {
      if (session.status === 'complete' || session.status === 'error') {
        this._sessions.delete(id);
      }
    }
  }
} 