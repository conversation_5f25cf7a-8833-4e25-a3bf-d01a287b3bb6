import { map } from 'nanostores';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Constants
export const MESSAGE_LIMIT_FREE_TIER = 10;
export const MESSAGE_LIMIT_STORAGE_KEY = 'zenvibe_message_counts';

// Types
type MessageCountData = {
  date: string;
  count: number;
};

type UserMessageCounts = Record<string, MessageCountData>;

// Store for message counts
export const messageLimitStore = map<{
  todayCount: number;
  isLimitReached: boolean;
  remainingMessages: number;
}>({
  todayCount: 0,
  isLimitReached: false,
  remainingMessages: MESSAGE_LIMIT_FREE_TIER,
});

// Initialize message count from storage
export const initializeMessageCount = (userId: string | undefined) => {
  if (!isBrowser || !userId) {
    return;
  }

  try {
    // Get the current date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];

    // Load existing counts from storage
    const storedCounts = localStorage.getItem(MESSAGE_LIMIT_STORAGE_KEY);
    const userCounts: UserMessageCounts = storedCounts ? JSON.parse(storedCounts) : {};

    // Check if we have counts for today for this user
    if (userCounts[userId] && userCounts[userId].date === today) {
      const count = userCounts[userId].count;
      messageLimitStore.set({
        todayCount: count,
        isLimitReached: count >= MESSAGE_LIMIT_FREE_TIER,
        remainingMessages: Math.max(0, MESSAGE_LIMIT_FREE_TIER - count),
      });
    } else {
      // Reset for a new day
      messageLimitStore.set({
        todayCount: 0,
        isLimitReached: false,
        remainingMessages: MESSAGE_LIMIT_FREE_TIER,
      });
    }
  } catch (error) {
    console.error('Error initializing message count:', error);
    // Set default values on error
    messageLimitStore.set({
      todayCount: 0,
      isLimitReached: false,
      remainingMessages: MESSAGE_LIMIT_FREE_TIER,
    });
  }
};

// Increment message count
export const incrementMessageCount = (userId: string | undefined) => {
  if (!isBrowser || !userId) {
    return false;
  }

  try {
    // Get the current date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];

    // Load existing counts from storage
    const storedCounts = localStorage.getItem(MESSAGE_LIMIT_STORAGE_KEY);
    const userCounts: UserMessageCounts = storedCounts ? JSON.parse(storedCounts) : {};

    // Initialize or update count for today
    if (!userCounts[userId] || userCounts[userId].date !== today) {
      userCounts[userId] = { date: today, count: 0 };
    }

    // Check if limit is already reached
    if (userCounts[userId].count >= MESSAGE_LIMIT_FREE_TIER) {
      messageLimitStore.set({
        todayCount: userCounts[userId].count,
        isLimitReached: true,
        remainingMessages: 0,
      });
      return false; // Cannot send more messages
    }

    // Increment the count
    userCounts[userId].count += 1;

    // Update the store
    const newCount = userCounts[userId].count;
    const isLimitReached = newCount >= MESSAGE_LIMIT_FREE_TIER;
    const remainingMessages = Math.max(0, MESSAGE_LIMIT_FREE_TIER - newCount);

    messageLimitStore.set({
      todayCount: newCount,
      isLimitReached,
      remainingMessages,
    });

    // Save back to localStorage
    localStorage.setItem(MESSAGE_LIMIT_STORAGE_KEY, JSON.stringify(userCounts));

    return !isLimitReached; // Return whether messages can still be sent
  } catch (error) {
    console.error('Error incrementing message count:', error);
    return true; // Allow sending on error to prevent blocking users unnecessarily
  }
};
