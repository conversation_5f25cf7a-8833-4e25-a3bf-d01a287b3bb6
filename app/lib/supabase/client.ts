import { createClient } from '@supabase/supabase-js';
import type { Database } from './database.types';

// Create a client factory to avoid issues with SSR
let supabaseInstance: ReturnType<typeof createClient<Database>> | null = null;

export const getSupabaseClient = () => {
  // Only create a client in browser environments
  if (typeof window === 'undefined') {
    // Return a dummy client for SSR that won't actually make any requests
    return {
      auth: {
        getSession: () => Promise.resolve({ data: { session: null }, error: null }),
        onAuthStateChange: () => ({
          data: {
            subscription: {
              unsubscribe: () => {
                /* Implementation not needed for SSR */
              },
            },
          },
        }),
      },
    } as unknown as ReturnType<typeof createClient<Database>>;
  }

  // Return existing client if we already created one
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Get environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables');
    throw new Error('Missing Supabase environment variables');
  }

  // Create a new client
  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      storage: {
        getItem: (key) => {
          try {
            return window.localStorage.getItem(key);
          } catch (error) {
            console.error('Error accessing localStorage', error);
            return null;
          }
        },
        setItem: (key, value) => {
          try {
            window.localStorage.setItem(key, value);
          } catch (error) {
            console.error('Error accessing localStorage', error);
          }
        },
        removeItem: (key) => {
          try {
            window.localStorage.removeItem(key);
          } catch (error) {
            console.error('Error accessing localStorage', error);
          }
        },
      },
    },
  });

  return supabaseInstance;
};

// For backwards compatibility
export const supabase = typeof window !== 'undefined' ? getSupabaseClient() : null;
