import type { ActionFunction } from '@remix-run/node';
import { json } from '@remix-run/node';
import { withSession } from '~/lib/generation/middleware';
import { GenerationPipeline } from '~/lib/generation/pipeline';
import type { ComponentResponse } from '~/types/generation';

export const action: ActionFunction = async ({ request }) => {
  return withSession(request, async (session) => {
    // Check if we have a plan in the session
    if (!session.data.plan) {
      return new Response('Generation plan not found', { status: 400 });
    }

    // Return the components from the session if available
    if (session.data.components) {
      return json(session.data.components as ComponentResponse);
    }

    // Start streaming generation
    const pipeline = GenerationPipeline.getInstance();
    const stream = await pipeline.startGeneration(session.id);

    // Return streaming response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });
  });
};
