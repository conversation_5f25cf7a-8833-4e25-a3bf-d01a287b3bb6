import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { getSupabaseClient } from '~/lib/supabase/client';

/**
 * Handle the OAuth callback and session establishment
 */
export default function AuthCallback() {
  const navigate = useNavigate();

  useEffect(() => {
    const processAuthCallback = async () => {
      try {
        // Get Supabase client
        const supabase = getSupabaseClient();

        // Get the session
        const { error } = await supabase.auth.getSession();

        // Handle authentication errors
        if (error) {
          console.error('Error getting auth session:', error);
          navigate('/signin?error=Unable to authenticate');

          return;
        }

        // Check if there's a stored redirect URL from the login page
        let redirectTo = '/';

        try {
          const storedRedirect = localStorage.getItem('authRedirectUrl');

          if (storedRedirect) {
            redirectTo = storedRedirect;

            // Clear the stored redirect URL
            localStorage.removeItem('authRedirectUrl');
          }
        } catch (storageErr) {
          console.error('Error accessing localStorage:', storageErr);
        }

        // Successfully authenticated, redirect to the stored URL or home page
        navigate(redirectTo);
      } catch (err) {
        console.error('Unexpected error during authentication:', err);
        navigate('/signin?error=Authentication failed');
      }
    };

    // Execute the auth callback handler
    processAuthCallback();
  }, [navigate]);

  // Show a loading state while processing the callback
  return (
    <div className="h-screen flex justify-center items-center">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Finalizing Authentication</h2>
        <p className="text-bolt-elements-textSecondary">Please wait while we complete the sign-in process...</p>
      </div>
    </div>
  );
}
