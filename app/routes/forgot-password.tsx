import { useState } from 'react';
import { Link } from '@remix-run/react';
import { getSupabaseClient } from '~/lib/supabase/client';
import BackgroundRays from '~/components/ui/BackgroundRays';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');
    setIsLoading(true);

    try {
      const supabase = getSupabaseClient();
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      setSuccessMsg('Password reset link sent! Check your email inbox.');
    } catch (error: any) {
      console.error('Error resetting password:', error);
      setErrorMsg(error.message || 'Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-bolt-elements-background-depth-1">
      <BackgroundRays />

      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="max-w-md w-full space-y-8 bg-bolt-elements-bg-depth-2 p-8 rounded-xl shadow-lg relative z-10">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-bolt-elements-textPrimary">Reset your password</h2>
            <p className="mt-2 text-bolt-elements-textSecondary">Enter your email to receive a password reset link</p>
          </div>

          {errorMsg && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
              <p className="text-red-700">{errorMsg}</p>
            </div>
          )}

          {successMsg && (
            <div className="bg-green-50 border-l-4 border-green-500 p-4 rounded">
              <p className="text-green-700">{successMsg}</p>
            </div>
          )}

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none rounded-lg relative block w-full px-3 py-3 border border-bolt-elements-borderColor placeholder-gray-500 text-bolt-elements-textPrimary focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Email address"
                disabled={isLoading || !!successMsg}
              />
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading || !!successMsg}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? 'Sending...' : 'Send reset link'}
              </button>
            </div>
          </form>

          <div className="text-center mt-4">
            <p className="text-sm text-bolt-elements-textSecondary">
              <Link to="/signin" className="font-medium text-blue-500 hover:text-blue-400">
                Back to login
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
