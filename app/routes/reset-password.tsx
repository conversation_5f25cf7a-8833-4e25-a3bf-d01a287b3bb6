import { useState, useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { getSupabaseClient } from '~/lib/supabase/client';
import BackgroundRays from '~/components/ui/BackgroundRays';

export default function ResetPassword() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hashAvailable, setHashAvailable] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if the URL contains a hash parameter (indicating password reset flow)
    const hash = window.location.hash;
    setHashAvailable(!!hash);

    if (!hash) {
      setErrorMsg('Invalid password reset link. Please request a new one.');
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');

    // Validate passwords match
    if (password !== confirmPassword) {
      setErrorMsg('Passwords do not match');
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setErrorMsg('Password must be at least 8 characters');
      return;
    }

    setIsLoading(true);

    try {
      const supabase = getSupabaseClient();
      const { error } = await supabase.auth.updateUser({ password });

      if (error) {
        throw error;
      }

      setSuccessMsg('Password updated successfully!');
      setTimeout(() => {
        navigate('/signin');
      }, 2000);
    } catch (error: any) {
      console.error('Error resetting password:', error);
      setErrorMsg(error.message || 'Failed to reset password');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-bolt-elements-background-depth-1">
      <BackgroundRays />
      
      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="max-w-md w-full space-y-8 bg-bolt-elements-bg-depth-2 p-8 rounded-xl shadow-lg relative z-10">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-bolt-elements-textPrimary">Reset your password</h2>
            <p className="mt-2 text-bolt-elements-textSecondary">Enter your new password below</p>
          </div>
          
          {errorMsg && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
              <p className="text-red-700">{errorMsg}</p>
            </div>
          )}

          {successMsg && (
            <div className="bg-green-50 border-l-4 border-green-500 p-4 rounded">
              <p className="text-green-700">{successMsg}</p>
            </div>
          )}

          {hashAvailable && (
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <div className="rounded-md shadow-sm space-y-4">
                <div>
                  <label htmlFor="password" className="sr-only">
                    New Password
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="appearance-none rounded-lg relative block w-full px-3 py-3 border border-bolt-elements-borderColor placeholder-gray-500 text-bolt-elements-textPrimary focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10"
                    placeholder="New password (min. 8 characters)"
                    disabled={isLoading || !!successMsg}
                  />
                </div>
                <div>
                  <label htmlFor="confirmPassword" className="sr-only">
                    Confirm Password
                  </label>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="appearance-none rounded-lg relative block w-full px-3 py-3 border border-bolt-elements-borderColor placeholder-gray-500 text-bolt-elements-textPrimary focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10"
                    placeholder="Confirm new password"
                    disabled={isLoading || !!successMsg}
                  />
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading || !!successMsg}
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? 'Updating password...' : 'Reset password'}
                </button>
              </div>
            </form>
          )}
          
          {!hashAvailable && (
            <div className="mt-4 text-center">
              <button
                onClick={() => navigate('/forgot-password')}
                className="text-blue-500 hover:text-blue-400 font-medium"
              >
                Request a new password reset link
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
