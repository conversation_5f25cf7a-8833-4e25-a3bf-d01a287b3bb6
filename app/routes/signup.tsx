import { useState } from 'react';
import { Link, useNavigate } from '@remix-run/react';
import { useAuth } from '~/contexts/AuthContext';

export default function Signup() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { signUp, signIn } = useAuth();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');

    // Validate passwords match
    if (password !== confirmPassword) {
      setErrorMsg('Passwords do not match');
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setErrorMsg('Password must be at least 8 characters');
      return;
    }

    setIsLoading(true);

    try {
      const { error, data } = await signUp(email, password);

      if (error) {
        throw error;
      }

      if (data?.user?.identities?.length === 0) {
        // User already exists
        setErrorMsg('An account with this email already exists');
      } else {
        // Show success message based on whether email confirmation is required
        if (data?.user?.confirmed_at) {
          // No email confirmation required, auto sign in
          const { error: signInError } = await signIn.email(email, password);

          if (signInError) {
            throw signInError;
          }

          navigate('/');
        } else {
          // Email confirmation required
          setSuccessMsg('Registration successful! Please check your email to confirm your account.');
        }
      }
    } catch (error: any) {
      console.error('Error signing up:', error);
      setErrorMsg(error.message || 'Failed to sign up');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    setErrorMsg('');
    setIsLoading(true);

    try {
      const { error } = await signIn.withGoogle();

      if (error) {
        throw error;
      }

      // The redirect is handled by Supabase OAuth
    } catch (error: any) {
      console.error('Error signing in with Google:', error);
      setErrorMsg(error.message || 'Failed to sign in with Google');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gray-950 text-white">
      <div className="w-full max-w-md p-8 space-y-8 bg-gray-900 rounded-lg shadow-xl">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 flex items-center justify-center text-3xl text-blue-500">
              <span className="i-ph:lightning-bold"></span>
            </div>
          </div>
          <h2 className="text-2xl font-bold">Create account</h2>
        </div>

        {errorMsg && (
          <div className="bg-red-900/30 border border-red-500/50 p-3 rounded text-sm text-red-200">
            <p>{errorMsg}</p>
          </div>
        )}

        {successMsg && (
          <div className="bg-green-900/30 border border-green-500/50 p-3 rounded text-sm text-green-200">
            <p>{successMsg}</p>
          </div>
        )}

        <button
          onClick={handleGoogleSignIn}
          disabled={isLoading || !!successMsg}
          className="w-full flex items-center justify-center gap-3 py-2.5 px-4 bg-gray-800 hover:bg-gray-700 rounded-md border border-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="h-5 w-5" viewBox="0 0 24 24">
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>
          Sign up with Google
        </button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-700"></div>
          </div>
          <div className="relative flex justify-center text-xs">
            <span className="px-2 bg-gray-900 text-gray-400">or</span>
          </div>
        </div>

        <form className="mt-8 space-y-5" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
              Email
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder:text-gray-500"
              placeholder="Enter your email address..."
              disabled={isLoading || !!successMsg}
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="new-password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder:text-gray-500"
              placeholder="Create a password..."
              disabled={isLoading || !!successMsg}
            />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-1">
              Confirm Password
            </label>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              autoComplete="new-password"
              required
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder:text-gray-500"
              placeholder="Confirm your password..."
              disabled={isLoading || !!successMsg}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading || !!successMsg}
            className="w-full py-2.5 px-4 bg-blue-600 hover:bg-blue-700 rounded-md text-white font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Creating account...' : 'Create account'}
          </button>
        </form>

        <div className="text-center mt-4">
          <p className="text-sm text-gray-400">
            Already have an account?{' '}
            <Link to="/signin" className="text-blue-400 hover:text-blue-300">
              Log in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
