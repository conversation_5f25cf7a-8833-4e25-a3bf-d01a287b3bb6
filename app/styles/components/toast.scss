.Toastify__toast {
  --at-apply: shadow-lg;
  
  background-color: var(--bolt-elements-bg-depth-2);
  color: var(--bolt-elements-textPrimary);
  border-radius: 12px;
  padding: 12px 16px;
  border: none;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  backdrop-filter: blur(6px);
  min-height: 60px;
  border-left: 4px solid transparent;
  overflow: hidden;
  
  &.Toastify__toast--success {
    border-left-color: var(--bolt-elements-icon-success);
  }
  
  &.Toastify__toast--error {
    border-left-color: var(--bolt-elements-icon-error);
  }
}

.Toastify__toast-body {
  padding: 0 8px;
  margin: 0;
  font-weight: 500;
}

.Toastify__progress-bar {
  height: 3px;
  opacity: 0.8;
}

.Toastify__close-button {
  color: var(--bolt-elements-item-contentDefault);
  opacity: 1;
  transition: all 0.2s ease;
  align-self: flex-start;
  padding: 4px;
  border-radius: 50%;
  
  &:hover {
    color: var(--bolt-elements-item-contentActive);
    background-color: rgba(0, 0, 0, 0.05);
  }
}
