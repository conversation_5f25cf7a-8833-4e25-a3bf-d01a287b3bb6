export interface ComponentResponse {
  id: string;
  components: {
    files: Array<{
      path: string;
      content: string;
    }>;
    imports: string[];
    dependencies: {
      added: string[];
      removed: string[];
    };
  };
  validation: {
    syntax: boolean;
    imports: boolean;
    types: boolean;
  };
  nextStep: 'integration' | 'complete';
}

export interface GenerationSession {
  id: string;
  stage: 'planning' | 'generation' | 'integration';
  status: 'pending' | 'processing' | 'complete' | 'error';
  data: {
    plan?: any;
    components?: ComponentResponse;
    integration?: any;
  };
  error?: string;
}

export interface GenerationProgress {
  sessionId: string;
  stage: GenerationSession['stage'];
  status: GenerationSession['status'];
  progress: number;
  currentStep: string;
  estimatedTime: number;
  error?: string;
} 